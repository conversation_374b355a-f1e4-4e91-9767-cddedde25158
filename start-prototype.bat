@echo off
echo ========================================
echo 商用空调监控软件 - 交互原型启动器
echo ========================================
echo.

echo 正在启动本地服务器...
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo 使用Python启动服务器...
    echo 访问地址: http://localhost:8000/prototype/
    echo.
    echo 登录信息:
    echo 用户名: admin
    echo 密码: admin
    echo.
    echo 按 Ctrl+C 停止服务器
    echo ========================================
    cd /d "%~dp0"
    python -m http.server 8000
) else (
    REM 检查Node.js是否可用
    node --version >nul 2>&1
    if %errorlevel% == 0 (
        echo 使用Node.js启动服务器...
        echo 访问地址: http://localhost:8000/prototype/
        echo.
        echo 登录信息:
        echo 用户名: admin
        echo 密码: admin
        echo.
        echo 按 Ctrl+C 停止服务器
        echo ========================================
        cd /d "%~dp0"
        npx serve . -p 8000
    ) else (
        echo 错误: 未找到Python或Node.js
        echo.
        echo 请安装以下任一工具:
        echo 1. Python 3.x (推荐)
        echo 2. Node.js
        echo.
        echo 或者直接用浏览器打开: prototype/index.html
        echo.
        pause
    )
)
