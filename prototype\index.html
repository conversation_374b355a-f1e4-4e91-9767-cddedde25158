<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商用空调监控系统 - 交互原型</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Segoe+UI:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 登录界面 -->
    <div id="loginPage" class="page active">
        <div class="login-container">
            <div class="login-header">
                <div class="logo">🏢</div>
                <h1>HVAC Monitor</h1>
                <p>商用空调监控调试系统</p>
            </div>
            
            <div class="login-form">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" placeholder="请输入用户名" required>
                    <div class="error-message" id="usernameError"></div>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <div class="password-input">
                        <input type="password" id="password" placeholder="请输入密码" required>
                        <button type="button" class="password-toggle" onclick="togglePassword()">👁️</button>
                    </div>
                    <div class="error-message" id="passwordError"></div>
                </div>
                
                <div class="form-options">
                    <label class="checkbox">
                        <input type="checkbox" id="rememberMe">
                        <span class="checkmark"></span>
                        记住登录状态
                    </label>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-primary" onclick="login()">
                        <span class="btn-text">登录</span>
                        <span class="btn-loading" style="display: none;">
                            <div class="spinner"></div>
                        </span>
                    </button>
                    <button type="button" class="btn btn-secondary">取消</button>
                </div>
                
                <div class="form-footer">
                    <div class="language-selector">
                        <label>语言:</label>
                        <select id="languageSelect" onchange="changeLanguage()">
                            <option value="zh-CN">中文</option>
                            <option value="en-US">English</option>
                            <option value="de-DE">Deutsch</option>
                        </select>
                    </div>
                    <div class="theme-selector">
                        <label>主题:</label>
                        <select id="themeSelect" onchange="changeTheme()">
                            <option value="light">浅色</option>
                            <option value="dark">深色</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="login-footer">
                <p>版本 v1.0.0 | <a href="#support">技术支持</a></p>
            </div>
        </div>
    </div>

    <!-- 主界面 -->
    <div id="mainPage" class="page">
        <!-- 标题栏 -->
        <div class="title-bar">
            <div class="title-left">
                <span class="app-title">商用空调监控系统</span>
            </div>
            <div class="title-right">
                <button class="icon-btn" title="最小化">−</button>
                <button class="icon-btn" title="最大化">□</button>
                <button class="icon-btn close" title="关闭">×</button>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <button class="icon-btn" onclick="refreshData()" title="刷新">🔄</button>
                <button class="icon-btn" onclick="exportData()" title="导出">📤</button>
                <button class="icon-btn" onclick="showSettings()" title="设置">⚙️</button>
            </div>
            <div class="toolbar-right">
                <select id="mainLanguageSelect" onchange="changeLanguage()">
                    <option value="zh-CN">中文</option>
                    <option value="en-US">English</option>
                </select>
                <div class="user-menu">
                    <button class="user-btn" onclick="toggleUserMenu()">
                        👤 管理员 ▼
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#profile">个人资料</a>
                        <a href="#settings">系统设置</a>
                        <hr>
                        <a href="#logout" onclick="logout()">退出登录</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <!-- 侧边栏 -->
            <div class="sidebar" id="sidebar">
                <nav class="nav-menu">
                    <a href="#dashboard" class="nav-item active" onclick="showPage('dashboard')">
                        📊 仪表板
                    </a>
                    <div class="nav-group">
                        <div class="nav-group-title">设备监控</div>
                        <a href="#monitor" class="nav-item" onclick="showPage('monitor')">
                            👁️ 实时监控
                        </a>
                        <a href="#history" class="nav-item" onclick="showPage('history')">
                            📈 历史数据
                        </a>
                        <a href="#alerts" class="nav-item" onclick="showPage('alerts')">
                            🔔 报警管理
                        </a>
                    </div>
                    <div class="nav-group">
                        <div class="nav-group-title">设备调试</div>
                        <a href="#debug" class="nav-item" onclick="showPage('debug')">
                            🔧 参数设置
                        </a>
                        <a href="#control" class="nav-item" onclick="showPage('control')">
                            🎮 控制操作
                        </a>
                        <a href="#diagnosis" class="nav-item" onclick="showPage('diagnosis')">
                            🔍 诊断工具
                        </a>
                    </div>
                    <div class="nav-group">
                        <div class="nav-group-title">系统管理</div>
                        <a href="#users" class="nav-item" onclick="showPage('users')">
                            👥 用户管理
                        </a>
                        <a href="#devices" class="nav-item" onclick="showPage('devices')">
                            📱 设备配置
                        </a>
                        <a href="#system" class="nav-item" onclick="showPage('system')">
                            ⚙️ 系统设置
                        </a>
                    </div>
                </nav>
            </div>

            <!-- 主内容区域 -->
            <div class="content-area">
                <!-- 仪表板页面 -->
                <div id="dashboardContent" class="content-page active">
                    <div class="page-header">
                        <h2>仪表板</h2>
                        <div class="breadcrumb">首页 > 仪表板</div>
                    </div>

                    <!-- 概览卡片 -->
                    <div class="overview-cards">
                        <div class="card overview-card" onclick="showDeviceDetails()">
                            <div class="card-icon">🏢</div>
                            <div class="card-content">
                                <h3>设备总数</h3>
                                <div class="card-value" id="totalDevices">128</div>
                                <div class="card-change positive">↑ +3 本周新增</div>
                            </div>
                        </div>

                        <div class="card overview-card" onclick="showOnlineDevices()">
                            <div class="card-icon">🟢</div>
                            <div class="card-content">
                                <h3>在线设备</h3>
                                <div class="card-value" id="onlineDevices">124</div>
                                <div class="card-change positive">↑ 97% 在线率</div>
                            </div>
                        </div>

                        <div class="card overview-card" onclick="showAlerts()">
                            <div class="card-icon">🔔</div>
                            <div class="card-content">
                                <h3>报警数量</h3>
                                <div class="card-value alert" id="alertCount">3</div>
                                <div class="card-change negative">↑ 2 新增报警</div>
                            </div>
                        </div>

                        <div class="card overview-card" onclick="showEnergyStats()">
                            <div class="card-icon">⚡</div>
                            <div class="card-content">
                                <h3>总能耗</h3>
                                <div class="card-value" id="totalPower">1.2MW</div>
                                <div class="card-change neutral">→ 较昨日持平</div>
                            </div>
                        </div>
                    </div>

                    <!-- 实时图表 -->
                    <div class="chart-section">
                        <div class="card">
                            <div class="card-header">
                                <h3>温度趋势 (过去24小时)</h3>
                                <div class="chart-controls">
                                    <button class="btn btn-small" onclick="changeTimeRange('1h')">1小时</button>
                                    <button class="btn btn-small active" onclick="changeTimeRange('24h')">24小时</button>
                                    <button class="btn btn-small" onclick="changeTimeRange('7d')">7天</button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="temperatureChart" width="800" height="300"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 设备状态列表 -->
                    <div class="device-list-section">
                        <div class="card">
                            <div class="card-header">
                                <h3>设备状态</h3>
                                <div class="list-controls">
                                    <input type="text" placeholder="搜索设备..." class="search-input" onkeyup="searchDevices(this.value)">
                                    <select onchange="filterDevices(this.value)">
                                        <option value="">全部状态</option>
                                        <option value="online">在线</option>
                                        <option value="offline">离线</option>
                                        <option value="warning">警告</option>
                                        <option value="error">故障</option>
                                    </select>
                                </div>
                            </div>
                            <div class="device-table">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>设备名称</th>
                                            <th>状态</th>
                                            <th>温度</th>
                                            <th>湿度</th>
                                            <th>功率</th>
                                            <th>最后更新</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="deviceTableBody">
                                        <!-- 设备数据将通过JavaScript动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 其他页面内容将在后续添加 -->
                <div id="monitorContent" class="content-page">
                    <div class="page-header">
                        <h2>实时监控</h2>
                        <div class="breadcrumb">首页 > 设备监控 > 实时监控</div>
                    </div>
                    <p>实时监控页面内容...</p>
                </div>

                <div id="debugContent" class="content-page">
                    <div class="page-header">
                        <h2>设备调试</h2>
                        <div class="breadcrumb">首页 > 设备调试 > 参数设置</div>
                    </div>
                    <p>设备调试页面内容...</p>
                </div>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span class="status-item">
                    <span class="status-indicator online"></span>
                    连接状态: 已连接
                </span>
                <span class="status-item">
                    最后同步: <span id="lastSync">2024-01-15 14:30:25</span>
                </span>
            </div>
            <div class="status-right">
                <span class="status-item">系统版本: v1.0.0</span>
            </div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notificationContainer" class="notification-container"></div>

    <!-- 模态对话框 -->
    <div id="modalOverlay" class="modal-overlay" onclick="closeModal()">
        <div class="modal" onclick="event.stopPropagation()">
            <div class="modal-header">
                <h3 id="modalTitle">标题</h3>
                <button class="modal-close" onclick="closeModal()">×</button>
            </div>
            <div class="modal-body" id="modalBody">
                内容
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="modalConfirm">确定</button>
                <button class="btn btn-secondary" onclick="closeModal()">取消</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
