/* 商用空调监控系统 - 样式表 */

/* 基础重置和变量 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Fluent Design 色彩系统 */
    --primary-color: #0078D4;
    --primary-hover: #106EBE;
    --primary-pressed: #005A9E;
    --secondary-color: #605E5C;
    --success-color: #107C10;
    --warning-color: #FF8C00;
    --error-color: #D13438;
    --info-color: #0078D4;
    
    /* 中性色彩 */
    --text-primary: #323130;
    --text-secondary: #605E5C;
    --text-disabled: #A19F9D;
    --border-color: #EDEBE9;
    --background-primary: #FFFFFF;
    --background-secondary: #FAF9F8;
    --background-disabled: #F3F2F1;
    
    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-xxl: 24px;
    --spacing-xxxl: 32px;
    
    /* 字体系统 */
    --font-family: 'Segoe UI', 'Microsoft YaHei UI', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-size-xs: 11px;
    --font-size-sm: 12px;
    --font-size-md: 14px;
    --font-size-lg: 16px;
    --font-size-xl: 20px;
    --font-size-xxl: 24px;
    --font-size-xxxl: 28px;
    
    /* 圆角和阴影 */
    --border-radius-sm: 2px;
    --border-radius-md: 4px;
    --border-radius-lg: 8px;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 16px rgba(0,0,0,0.2);
}

/* 基础样式 */
body {
    font-family: var(--font-family);
    font-size: var(--font-size-md);
    color: var(--text-primary);
    background-color: var(--background-secondary);
    overflow: hidden;
}

/* 页面容器 */
.page {
    display: none;
    width: 100vw;
    height: 100vh;
}

.page.active {
    display: flex;
    flex-direction: column;
}

/* 登录页面样式 */
#loginPage {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    justify-content: center;
    align-items: center;
}

.login-container {
    background: var(--background-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xxxl);
    width: 400px;
    text-align: center;
}

.login-header {
    margin-bottom: var(--spacing-xxl);
}

.logo {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
}

.login-header h1 {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.login-header p {
    color: var(--text-secondary);
    font-size: var(--font-size-md);
}

/* 表单样式 */
.form-group {
    margin-bottom: var(--spacing-lg);
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 600;
    color: var(--text-primary);
}

.form-group input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-md);
    transition: border-color 0.2s ease;
}

.form-group input:focus {
    outline: none;
    border: 2px solid var(--primary-color);
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: var(--spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    font-size: var(--font-size-md);
}

.error-message {
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    min-height: 18px;
}

.form-options {
    margin-bottom: var(--spacing-lg);
    text-align: left;
}

.checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: var(--font-size-sm);
}

.checkbox input {
    margin-right: var(--spacing-sm);
}

/* 按钮样式 */
.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    min-width: 80px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
}

.btn-primary:active {
    background-color: var(--primary-pressed);
    transform: translateY(0);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--background-disabled);
}

.btn-small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    height: 24px;
    min-width: 60px;
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.form-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.language-selector, .theme-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.language-selector select, .theme-selector select {
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
}

.login-footer {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.login-footer a {
    color: var(--primary-color);
    text-decoration: none;
}

/* 加载动画 */
.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255,255,255,0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 主界面样式 */
#mainPage {
    background-color: var(--background-secondary);
}

/* 标题栏 */
.title-bar {
    height: 32px;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--spacing-md);
    -webkit-app-region: drag;
}

.title-left {
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.title-right {
    display: flex;
    gap: var(--spacing-xs);
    -webkit-app-region: no-drag;
}

.icon-btn {
    width: 24px;
    height: 24px;
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-sm);
    transition: background-color 0.2s ease;
}

.icon-btn:hover {
    background-color: rgba(255,255,255,0.1);
}

.icon-btn.close:hover {
    background-color: var(--error-color);
}

/* 工具栏 */
.toolbar {
    height: 48px;
    background-color: var(--background-primary);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--spacing-lg);
}

.toolbar-left, .toolbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-menu {
    position: relative;
}

.user-btn {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    cursor: pointer;
    border-radius: var(--border-radius-sm);
    transition: background-color 0.2s ease;
}

.user-btn:hover {
    background-color: var(--background-disabled);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    min-width: 150px;
    z-index: 1000;
    display: none;
}

.user-dropdown.show {
    display: block;
}

.user-dropdown a {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.user-dropdown a:hover {
    background-color: var(--background-disabled);
}

.user-dropdown hr {
    border: none;
    border-top: 1px solid var(--border-color);
    margin: var(--spacing-xs) 0;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 侧边栏 */
.sidebar {
    width: 240px;
    background-color: var(--background-primary);
    border-right: 1px solid var(--border-color);
    overflow-y: auto;
}

.nav-menu {
    padding: var(--spacing-lg);
}

.nav-item {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-xs);
    transition: all 0.2s ease;
}

.nav-item:hover {
    background-color: var(--background-disabled);
}

.nav-item.active {
    background-color: var(--primary-color);
    color: white;
}

.nav-group {
    margin-bottom: var(--spacing-lg);
}

.nav-group-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    padding: 0 var(--spacing-md);
}

/* 内容区域 */
.content-area {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-xxl);
}

.content-page {
    display: none;
}

.content-page.active {
    display: block;
}

.page-header {
    margin-bottom: var(--spacing-xxl);
}

.page-header h2 {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.breadcrumb {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* 卡片样式 */
.card {
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-xxl);
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

/* 概览卡片 */
.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xxl);
}

.overview-card {
    padding: var(--spacing-lg);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.card-icon {
    font-size: 32px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--background-secondary);
    border-radius: var(--border-radius-md);
}

.card-content h3 {
    font-size: var(--font-size-md);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.card-value {
    font-size: var(--font-size-xxxl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.card-value.alert {
    color: var(--error-color);
}

.card-change {
    font-size: var(--font-size-sm);
}

.card-change.positive {
    color: var(--success-color);
}

.card-change.negative {
    color: var(--error-color);
}

.card-change.neutral {
    color: var(--text-secondary);
}

/* 状态栏 */
.status-bar {
    height: 24px;
    background-color: var(--background-primary);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--spacing-lg);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.status-left, .status-right {
    display: flex;
    gap: var(--spacing-lg);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: var(--spacing-xs);
}

.status-indicator.online {
    background-color: var(--success-color);
}

.status-indicator.offline {
    background-color: var(--text-disabled);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .sidebar {
        width: 60px;
    }
    
    .nav-item {
        text-align: center;
        padding: var(--spacing-md);
    }
    
    .nav-group-title {
        display: none;
    }
}

/* 深色主题 */
[data-theme="dark"] {
    --text-primary: #FFFFFF;
    --text-secondary: #C8C6C4;
    --text-disabled: #8A8886;
    --border-color: #323130;
    --background-primary: #1B1A19;
    --background-secondary: #252423;
    --background-disabled: #323130;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* 数值更新动画 */
.number-update {
    animation: numberPulse 0.5s ease-out;
}

@keyframes numberPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); color: var(--primary-color); }
    100% { transform: scale(1); }
}

/* 表格样式 */
.device-table {
    overflow-x: auto;
}

.device-table table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
}

.device-table th {
    background-color: var(--background-secondary);
    padding: var(--spacing-md);
    text-align: left;
    font-weight: 600;
    color: var(--text-secondary);
    border-bottom: 1px solid var(--border-color);
}

.device-table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.device-table tr:hover {
    background-color: var(--background-disabled);
}

.device-name strong {
    color: var(--text-primary);
    font-weight: 600;
}

.device-location {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-top: 2px;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.status-online {
    background-color: #F3F9F1;
    color: var(--success-color);
}

.status-offline {
    background-color: var(--background-disabled);
    color: var(--text-disabled);
}

.status-warning {
    background-color: #FFF8F0;
    color: var(--warning-color);
}

.status-error {
    background-color: #FDF3F4;
    color: var(--error-color);
}

.text-right {
    text-align: right;
}

.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
}

.search-input {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    width: 200px;
}

.list-controls {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.list-controls select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
}

/* 图表容器 */
.chart-section {
    margin-bottom: var(--spacing-xxl);
}

.chart-container {
    padding: var(--spacing-lg);
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--background-secondary);
    border-radius: var(--border-radius-sm);
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
}

.chart-controls {
    display: flex;
    gap: var(--spacing-xs);
}

.chart-controls .btn.active {
    background-color: var(--primary-color);
    color: white;
}
