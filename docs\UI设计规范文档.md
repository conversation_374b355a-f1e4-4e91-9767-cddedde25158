# 商用空调监控调试软件 UI/UX 设计规范

## 📋 项目概述

**项目名称：** 商用空调监控调试软件  
**目标平台：** Windows 7+ (.NET/WPF)  
**设计风格：** Microsoft Fluent Design System  
**主题方案：** 浅色主题优先，支持深色主题切换  
**国际化：** 支持多语言本地化  
**目标用户：** HVAC专业技术人员、设备维护工程师、系统管理员  

---

## 🎨 设计系统规范

### 1. 色彩系统 (Color Palette)

#### 主色调 (Primary Colors)
```
品牌主色：#0078D4 (Fluent Blue)
主色深色：#106EBE
主色浅色：#40E0FF
主色禁用：#A6A6A6
```

#### 语义色彩 (Semantic Colors)
```
成功状态：#107C10 (绿色)
警告状态：#FF8C00 (橙色) 
错误状态：#D13438 (红色)
信息提示：#0078D4 (蓝色)
```

#### 中性色彩 (Neutral Colors)
```
文本主色：#323130
文本次要：#605E5C
文本禁用：#A19F9D
边框颜色：#EDEBE9
背景主色：#FFFFFF
背景次要：#FAF9F8
背景禁用：#F3F2F1
```

#### 数据可视化色彩
```
温度指示：#FF6B35 (暖橙)
湿度指示：#4ECDC4 (青绿)
压力指示：#45B7D1 (天蓝)
功率指示：#96CEB4 (薄荷绿)
```

### 2. 字体系统 (Typography)

#### 字体族
```
西文字体：Segoe UI, -apple-system, BlinkMacSystemFont
中文字体：Microsoft YaHei UI, 微软雅黑
等宽字体：Consolas, Monaco, monospace
```

#### 字体层级
```
H1 标题：28px, Bold, #323130
H2 标题：24px, SemiBold, #323130  
H3 标题：20px, SemiBold, #323130
H4 标题：16px, SemiBold, #323130
正文大：16px, Regular, #323130
正文中：14px, Regular, #323130
正文小：12px, Regular, #605E5C
说明文字：11px, Regular, #605E5C
```

### 3. 间距系统 (Spacing)

#### 基础间距单位
```
基础单位：4px
常用间距：
- 4px (极小)
- 8px (小)
- 12px (中小)
- 16px (中)
- 20px (中大)
- 24px (大)
- 32px (极大)
- 48px (超大)
```

#### 布局间距
```
页面边距：24px
卡片内边距：16px
组件间距：12px
表单间距：8px
按钮内边距：8px 16px
```

### 4. 圆角系统 (Border Radius)
```
小圆角：2px (按钮、输入框)
中圆角：4px (卡片、面板)
大圆角：8px (模态框、大容器)
```

### 5. 阴影系统 (Elevation)
```
卡片阴影：0 2px 4px rgba(0,0,0,0.1)
悬浮阴影：0 4px 8px rgba(0,0,0,0.15)
模态阴影：0 8px 16px rgba(0,0,0,0.2)
```

---

## 🧩 组件库规范

### 1. 按钮组件 (Buttons)

#### 主要按钮 (Primary Button)
```
背景色：#0078D4
文字色：#FFFFFF
悬停：#106EBE
按下：#005A9E
禁用：#F3F2F1 背景，#A19F9D 文字
```

#### 次要按钮 (Secondary Button)
```
背景色：透明
边框：1px solid #0078D4
文字色：#0078D4
悬停：#F3F2F1 背景
```

#### 危险按钮 (Danger Button)
```
背景色：#D13438
文字色：#FFFFFF
悬停：#A4262C
```

### 2. 输入组件 (Input Controls)

#### 文本输入框
```
边框：1px solid #EDEBE9
聚焦边框：2px solid #0078D4
背景：#FFFFFF
内边距：8px 12px
高度：32px
```

#### 下拉选择框
```
与文本输入框样式保持一致
下拉箭头：#605E5C
选项悬停：#F3F2F1
```

### 3. 数据展示组件

#### 数据卡片
```
背景：#FFFFFF
边框：1px solid #EDEBE9
圆角：4px
内边距：16px
阴影：0 2px 4px rgba(0,0,0,0.1)
```

#### 状态指示器
```
在线：绿色圆点 #107C10
离线：灰色圆点 #A19F9D
警告：橙色圆点 #FF8C00
错误：红色圆点 #D13438
```

---

## 📱 界面布局规范

### 1. 主界面布局 (Main Layout)

#### 整体结构
```
┌─────────────────────────────────────────┐
│ 标题栏 (Title Bar) - 32px               │
├─────────────────────────────────────────┤
│ 工具栏 (Toolbar) - 48px                 │
├─────────────────────────────────────────┤
│ ┌─────────┬─────────────────────────────┐ │
│ │ 侧边栏  │ 主内容区域                  │ │
│ │ 240px   │ (Main Content)              │ │
│ │         │                             │ │
│ │         │                             │ │
│ └─────────┴─────────────────────────────┘ │
├─────────────────────────────────────────┤
│ 状态栏 (Status Bar) - 24px              │
└─────────────────────────────────────────┘
```

#### 响应式断点
```
最小宽度：1024px
推荐宽度：1366px
最大宽度：无限制
侧边栏可折叠：< 1200px 时自动折叠
```

### 2. 导航结构

#### 主导航 (侧边栏)
```
- 仪表板 (Dashboard)
- 设备监控 (Device Monitor)
  - 实时监控
  - 历史数据
  - 报警管理
- 设备调试 (Device Debug)
  - 参数设置
  - 控制操作
  - 诊断工具
- 系统管理 (System)
  - 用户管理
  - 设备配置
  - 系统设置
```

#### 面包屑导航
```
位置：主内容区域顶部
格式：首页 > 设备监控 > 实时监控
分隔符：>
颜色：#605E5C
```

---

## 🎯 交互设计原则

### 1. 用户体验原则

#### 一致性 (Consistency)
- 相同功能使用相同的交互模式
- 保持视觉元素的一致性
- 遵循Windows平台交互习惯

#### 反馈性 (Feedback)
- 所有用户操作都要有明确反馈
- 加载状态使用进度指示器
- 成功/失败操作显示相应提示

#### 容错性 (Error Prevention)
- 重要操作需要确认对话框
- 提供撤销功能
- 输入验证和错误提示

### 2. 操作效率优化

#### 快捷键支持
```
Ctrl+N：新建
Ctrl+S：保存
Ctrl+F：搜索
F5：刷新
Esc：取消/关闭
```

#### 批量操作
- 支持多选操作
- 提供批量设置功能
- 快速筛选和搜索

---

## 🌐 国际化设计规范

### 1. 文本处理

#### 文本长度适配
```
英文基准长度：100%
中文长度预留：120%
德文长度预留：140%
俄文长度预留：160%
```

#### 文本对齐
```
左对齐语言：英文、中文、德文等
右对齐语言：阿拉伯文、希伯来文
```

### 2. 日期时间格式
```
中文：YYYY年MM月DD日 HH:mm:ss
英文：MM/DD/YYYY HH:mm:ss
德文：DD.MM.YYYY HH:mm:ss
```

### 3. 数值格式
```
温度单位：°C / °F 可切换
压力单位：kPa / PSI 可切换
小数分隔符：根据地区设置
千位分隔符：根据地区设置
```

---

## ♿ 无障碍设计规范

### 1. 颜色对比度
```
正常文本：4.5:1 最小对比度
大文本：3:1 最小对比度
非文本元素：3:1 最小对比度
```

### 2. 键盘导航
- 所有交互元素支持Tab键导航
- 焦点指示器清晰可见
- 支持快捷键操作

### 3. 屏幕阅读器支持
- 为图像提供alt文本
- 使用语义化的HTML结构
- 提供ARIA标签支持

---

## 📊 性能设计要求

### 1. 界面响应时间
```
页面切换：< 200ms
数据加载：< 500ms
图表渲染：< 1000ms
```

### 2. 动画效果
```
过渡动画：200-300ms
缓动函数：ease-out
避免过度动画影响性能
```

---

## 🖥️ 具体界面设计方案

### 1. 主仪表板界面 (Dashboard)

#### 布局结构
```
┌─────────────────────────────────────────────────────────────┐
│ 工具栏：刷新 | 导出 | 设置 | 语言切换 | 用户菜单           │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────┬─────────────────────────────────────────────────┐ │
│ │ 导航栏  │ 概览卡片区域                                    │ │
│ │         │ ┌─────────┬─────────┬─────────┬─────────┐       │ │
│ │ • 仪表板│ │设备总数 │在线设备 │报警数量 │能耗统计 │       │ │
│ │ • 监控  │ │  128    │  124    │   3     │ 1.2MW   │       │ │
│ │ • 调试  │ └─────────┴─────────┴─────────┴─────────┘       │ │
│ │ • 管理  │                                                 │ │
│ │         │ 实时监控图表区域                                │ │
│ │         │ ┌─────────────────────────────────────────────┐ │ │
│ │         │ │ 温度趋势图 (过去24小时)                     │ │ │
│ │         │ │ [折线图显示各区域温度变化]                  │ │ │
│ │         │ └─────────────────────────────────────────────┘ │ │
│ │         │                                                 │ │
│ │         │ 设备状态列表                                    │ │
│ │         │ ┌─────────────────────────────────────────────┐ │ │
│ │         │ │ 设备名称 | 状态 | 温度 | 湿度 | 最后更新    │ │ │
│ │         │ │ AC-001   | 🟢   | 22°C | 45% | 2分钟前     │ │ │
│ │         │ │ AC-002   | 🟡   | 25°C | 50% | 1分钟前     │ │ │
│ │         │ └─────────────────────────────────────────────┘ │ │
│ └─────────┴─────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 状态栏：连接状态 | 最后同步时间 | 系统版本                   │
└─────────────────────────────────────────────────────────────┘
```

#### 关键交互元素
- **概览卡片**：点击跳转到详细页面
- **图表区域**：支持缩放、时间范围选择
- **设备列表**：支持排序、筛选、搜索
- **状态指示器**：实时更新，颜色编码

### 2. 设备监控界面 (Device Monitor)

#### 实时监控视图
```
┌─────────────────────────────────────────────────────────────┐
│ 筛选工具栏：区域选择 | 设备类型 | 状态筛选 | 搜索框         │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────┬─────────────────────────────────────────────────┐ │
│ │ 楼层/   │ 设备网格视图                                    │ │
│ │ 区域树  │ ┌─────┬─────┬─────┬─────┬─────┬─────┐           │ │
│ │         │ │AC001│AC002│AC003│AC004│AC005│AC006│           │ │
│ │ ▼ 1楼   │ │22°C │25°C │23°C │24°C │21°C │26°C │           │ │
│ │  • 大厅 │ │ 🟢  │ 🟡  │ 🟢  │ 🟢  │ 🟢  │ 🔴  │           │ │
│ │  • 会议室│ └─────┴─────┴─────┴─────┴─────┴─────┘           │ │
│ │ ▼ 2楼   │                                                 │ │
│ │  • 办公区│ 详细信息面板 (选中设备时显示)                   │ │
│ │         │ ┌─────────────────────────────────────────────┐ │ │
│ │         │ │ 设备：AC-006 | 状态：故障                   │ │ │
│ │         │ │ ┌─────────┬─────────┬─────────┬─────────┐   │ │ │
│ │         │ │ │ 温度    │ 湿度    │ 压力    │ 功率    │   │ │ │
│ │         │ │ │ 26°C    │ 65%     │ 2.1kPa  │ 1.2kW   │   │ │ │
│ │         │ │ │ ⚠️异常  │ 正常    │ 正常    │ 正常    │   │ │ │
│ │         │ │ └─────────┴─────────┴─────────┴─────────┘   │ │ │
│ │         │ │ [实时图表] [历史数据] [控制面板]            │ │ │
│ │         │ └─────────────────────────────────────────────┘ │ │
│ └─────────┴─────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 3. 设备调试界面 (Device Debug)

#### 参数设置面板
```
┌─────────────────────────────────────────────────────────────┐
│ 设备选择：[下拉选择] AC-001 | 连接状态：🟢 已连接           │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────┬─────────────────────────────────────────┐ │
│ │ 参数设置        │ 实时监控                                │ │
│ │                 │                                         │ │
│ │ 目标温度        │ ┌─────────────────────────────────────┐ │ │
│ │ [22] °C         │ │ 温度曲线图                          │ │ │
│ │ ┌─────┬─────┐   │ │ [实时更新的折线图]                  │ │ │
│ │ │ -1  │ +1  │   │ │                                     │ │ │
│ │ └─────┴─────┘   │ └─────────────────────────────────────┘ │ │
│ │                 │                                         │ │
│ │ 风速设置        │ 控制操作                                │ │
│ │ ○ 低速          │ ┌─────────┬─────────┬─────────┐         │ │
│ │ ● 中速          │ │ 启动    │ 停止    │ 重启    │         │ │
│ │ ○ 高速          │ │ [按钮]  │ [按钮]  │ [按钮]  │         │ │
│ │ ○ 自动          │ └─────────┴─────────┴─────────┘         │ │
│ │                 │                                         │ │
│ │ 模式选择        │ 诊断信息                                │ │
│ │ [制冷 ▼]        │ ┌─────────────────────────────────────┐ │ │
│ │                 │ │ • 压缩机运行正常                    │ │ │
│ │ 定时设置        │ │ • 传感器读数稳定                    │ │ │
│ │ ☑ 启用定时      │ │ • 通信连接良好                      │ │ │
│ │ 开始：[08:00]   │ │ ⚠ 过滤网需要清洁                   │ │ │
│ │ 结束：[18:00]   │ └─────────────────────────────────────┘ │ │
│ │                 │                                         │ │
│ │ ┌─────────────┐ │ ┌─────────────────────────────────────┐ │ │
│ │ │ 应用设置    │ │ │ 导出诊断报告                        │ │ │
│ │ └─────────────┘ │ └─────────────────────────────────────┘ │ │
│ └─────────────────┴─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 🎮 交互原型设计规范

### 1. 页面转场动画
```css
/* 页面切换动画 */
.page-transition {
    transition: all 0.3s ease-out;
    transform: translateX(0);
}

.page-enter {
    transform: translateX(100%);
    opacity: 0;
}

.page-exit {
    transform: translateX(-100%);
    opacity: 0;
}
```

### 2. 悬停效果
```css
/* 卡片悬停效果 */
.device-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: all 0.2s ease-out;
}

/* 按钮悬停效果 */
.btn-primary:hover {
    background-color: #106EBE;
    transform: translateY(-1px);
}
```

### 3. 加载状态
```
加载指示器类型：
- 页面加载：骨架屏 + 进度条
- 数据刷新：旋转图标
- 操作处理：按钮内加载动画
- 图表加载：渐进式数据显示
```

### 4. 状态反馈
```
成功操作：
- 绿色勾选图标
- 淡入淡出提示消息
- 持续时间：3秒

错误操作：
- 红色警告图标
- 错误信息详细说明
- 提供解决建议
- 手动关闭或5秒自动消失
```

---

## 📱 响应式设计适配

### 1. 屏幕尺寸适配
```
超大屏 (≥1920px)：
- 侧边栏固定展开
- 主内容区域最大宽度1600px
- 显示更多数据列

大屏 (1366px-1919px)：
- 标准布局
- 侧边栏可折叠
- 完整功能显示

中屏 (1024px-1365px)：
- 侧边栏默认折叠
- 减少数据列显示
- 优化间距

小屏 (<1024px)：
- 不支持，显示升级提示
```

### 2. 高DPI显示适配
```
DPI缩放支持：
- 100% (标准)
- 125% (推荐)
- 150% (高DPI)
- 200% (超高DPI)

图标资源：
- 16x16, 24x24, 32x32 (标准)
- 20x20, 30x30, 40x40 (125%)
- 24x24, 36x36, 48x48 (150%)
- 32x32, 48x48, 64x64 (200%)
```

---

*本文档将持续更新，确保设计规范与项目需求保持同步。*
