// 商用空调监控系统 - 交互脚本

// 全局变量
let currentLanguage = 'zh-CN';
let currentTheme = 'light';
let deviceData = [];
let isLoggedIn = false;

// 多语言文本
const translations = {
    'zh-CN': {
        'login': '登录',
        'username': '用户名',
        'password': '密码',
        'remember': '记住登录状态',
        'cancel': '取消',
        'dashboard': '仪表板',
        'monitor': '实时监控',
        'debug': '设备调试',
        'totalDevices': '设备总数',
        'onlineDevices': '在线设备',
        'alertCount': '报警数量',
        'totalPower': '总能耗',
        'connecting': '连接中...',
        'loginSuccess': '登录成功',
        'loginFailed': '登录失败，请检查用户名和密码'
    },
    'en-US': {
        'login': 'Login',
        'username': 'Username',
        'password': 'Password',
        'remember': 'Remember me',
        'cancel': 'Cancel',
        'dashboard': 'Dashboard',
        'monitor': 'Monitor',
        'debug': 'Debug',
        'totalDevices': 'Total Devices',
        'onlineDevices': 'Online Devices',
        'alertCount': 'Alerts',
        'totalPower': 'Total Power',
        'connecting': 'Connecting...',
        'loginSuccess': 'Login successful',
        'loginFailed': 'Login failed, please check username and password'
    }
};

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    generateDeviceData();
    updateDeviceTable();
    startDataSimulation();
});

// 应用初始化
function initializeApp() {
    // 设置默认语言和主题
    changeLanguage();
    changeTheme();
    
    // 绑定事件监听器
    bindEventListeners();
    
    // 初始化图表
    initializeCharts();
}

// 绑定事件监听器
function bindEventListeners() {
    // 键盘事件
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && document.getElementById('loginPage').classList.contains('active')) {
            login();
        }
        if (e.key === 'Escape') {
            closeModal();
        }
    });
    
    // 窗口大小变化
    window.addEventListener('resize', function() {
        handleResize();
    });
}

// 密码显示/隐藏切换
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleBtn = document.querySelector('.password-toggle');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleBtn.textContent = '🙈';
    } else {
        passwordInput.type = 'password';
        toggleBtn.textContent = '👁️';
    }
}

// 登录功能
function login() {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const loginBtn = document.querySelector('.btn-primary');
    const btnText = loginBtn.querySelector('.btn-text');
    const btnLoading = loginBtn.querySelector('.btn-loading');
    
    // 清除之前的错误信息
    clearErrors();
    
    // 验证输入
    if (!validateLogin(username, password)) {
        return;
    }
    
    // 显示加载状态
    btnText.style.display = 'none';
    btnLoading.style.display = 'flex';
    loginBtn.disabled = true;
    
    // 模拟登录请求
    setTimeout(() => {
        if (username === 'admin' && password === 'admin') {
            // 登录成功
            showNotification(getTranslation('loginSuccess'), 'success');
            switchToMainPage();
        } else {
            // 登录失败
            showError('usernameError', getTranslation('loginFailed'));
            shakeElement(document.querySelector('.login-form'));
        }
        
        // 恢复按钮状态
        btnText.style.display = 'block';
        btnLoading.style.display = 'none';
        loginBtn.disabled = false;
    }, 1500);
}

// 验证登录输入
function validateLogin(username, password) {
    let isValid = true;
    
    if (!username) {
        showError('usernameError', '请输入用户名');
        isValid = false;
    }
    
    if (!password) {
        showError('passwordError', '请输入密码');
        isValid = false;
    }
    
    return isValid;
}

// 显示错误信息
function showError(elementId, message) {
    const errorElement = document.getElementById(elementId);
    errorElement.textContent = message;
    errorElement.style.color = 'var(--error-color)';
}

// 清除错误信息
function clearErrors() {
    const errorElements = document.querySelectorAll('.error-message');
    errorElements.forEach(element => {
        element.textContent = '';
    });
}

// 元素抖动效果
function shakeElement(element) {
    element.style.animation = 'shake 0.5s ease-in-out';
    setTimeout(() => {
        element.style.animation = '';
    }, 500);
}

// 切换到主页面
function switchToMainPage() {
    const loginPage = document.getElementById('loginPage');
    const mainPage = document.getElementById('mainPage');
    
    loginPage.classList.remove('active');
    mainPage.classList.add('active');
    
    isLoggedIn = true;
    updateLastSyncTime();
}

// 退出登录
function logout() {
    const loginPage = document.getElementById('loginPage');
    const mainPage = document.getElementById('mainPage');
    
    mainPage.classList.remove('active');
    loginPage.classList.add('active');
    
    isLoggedIn = false;
    
    // 清除登录表单
    document.getElementById('username').value = '';
    document.getElementById('password').value = '';
    
    showNotification('已退出登录', 'info');
}

// 语言切换
function changeLanguage() {
    const select = document.getElementById('languageSelect') || document.getElementById('mainLanguageSelect');
    if (select) {
        currentLanguage = select.value;
    }
    
    // 更新界面文字
    updateUIText();
}

// 主题切换
function changeTheme() {
    const select = document.getElementById('themeSelect');
    if (select) {
        currentTheme = select.value;
        document.documentElement.setAttribute('data-theme', currentTheme);
    }
}

// 获取翻译文本
function getTranslation(key) {
    return translations[currentLanguage][key] || key;
}

// 更新界面文字
function updateUIText() {
    // 这里可以添加更多的文本更新逻辑
    console.log('Language changed to:', currentLanguage);
}

// 显示页面
function showPage(pageId) {
    // 隐藏所有页面
    const pages = document.querySelectorAll('.content-page');
    pages.forEach(page => page.classList.remove('active'));
    
    // 显示目标页面
    const targetPage = document.getElementById(pageId + 'Content');
    if (targetPage) {
        targetPage.classList.add('active');
    }
    
    // 更新导航状态
    updateNavigation(pageId);
}

// 更新导航状态
function updateNavigation(activePageId) {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.classList.remove('active');
        if (item.getAttribute('href') === '#' + activePageId) {
            item.classList.add('active');
        }
    });
}

// 用户菜单切换
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    dropdown.classList.toggle('show');
}

// 点击外部关闭用户菜单
document.addEventListener('click', function(e) {
    const userMenu = document.querySelector('.user-menu');
    const dropdown = document.getElementById('userDropdown');
    
    if (!userMenu.contains(e.target)) {
        dropdown.classList.remove('show');
    }
});

// 生成模拟设备数据
function generateDeviceData() {
    deviceData = [];
    const deviceTypes = ['AC', 'VRF', 'FCU'];
    const statuses = ['online', 'offline', 'warning', 'error'];
    const statusWeights = [0.7, 0.1, 0.15, 0.05]; // 权重分布
    
    for (let i = 1; i <= 128; i++) {
        const deviceType = deviceTypes[Math.floor(Math.random() * deviceTypes.length)];
        const deviceId = `${deviceType}-${String(i).padStart(3, '0')}`;
        
        // 根据权重随机选择状态
        const status = weightedRandomChoice(statuses, statusWeights);
        
        deviceData.push({
            id: deviceId,
            name: deviceId,
            status: status,
            temperature: Math.round((Math.random() * 10 + 18) * 10) / 10, // 18-28°C
            humidity: Math.round((Math.random() * 30 + 40)), // 40-70%
            power: Math.round((Math.random() * 2 + 0.5) * 100) / 100, // 0.5-2.5kW
            lastUpdate: new Date(Date.now() - Math.random() * 300000), // 最近5分钟内
            floor: Math.floor((i - 1) / 16) + 1, // 每层16台设备
            zone: ['大厅', '办公区', '会议室', '休息区'][Math.floor(Math.random() * 4)]
        });
    }
}

// 权重随机选择
function weightedRandomChoice(choices, weights) {
    const random = Math.random();
    let weightSum = 0;
    
    for (let i = 0; i < choices.length; i++) {
        weightSum += weights[i];
        if (random <= weightSum) {
            return choices[i];
        }
    }
    
    return choices[choices.length - 1];
}

// 更新设备表格
function updateDeviceTable() {
    const tbody = document.getElementById('deviceTableBody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    // 只显示前20个设备
    const displayDevices = deviceData.slice(0, 20);
    
    displayDevices.forEach(device => {
        const row = createDeviceRow(device);
        tbody.appendChild(row);
    });
}

// 创建设备行
function createDeviceRow(device) {
    const row = document.createElement('tr');
    row.className = 'device-row';
    row.setAttribute('data-device-id', device.id);
    
    const statusIcon = getStatusIcon(device.status);
    const statusText = getStatusText(device.status);
    const timeAgo = getTimeAgo(device.lastUpdate);
    
    row.innerHTML = `
        <td>
            <div class="device-name">
                <strong>${device.name}</strong>
                <div class="device-location">${device.floor}楼 ${device.zone}</div>
            </div>
        </td>
        <td>
            <span class="status-badge status-${device.status}">
                ${statusIcon} ${statusText}
            </span>
        </td>
        <td class="text-right">${device.temperature}°C</td>
        <td class="text-right">${device.humidity}%</td>
        <td class="text-right">${device.power}kW</td>
        <td class="text-right">${timeAgo}</td>
        <td>
            <div class="action-buttons">
                <button class="btn btn-small" onclick="viewDevice('${device.id}')">查看</button>
                <button class="btn btn-small" onclick="debugDevice('${device.id}')">调试</button>
            </div>
        </td>
    `;
    
    return row;
}

// 获取状态图标
function getStatusIcon(status) {
    const icons = {
        'online': '🟢',
        'offline': '⚫',
        'warning': '🟡',
        'error': '🔴'
    };
    return icons[status] || '⚫';
}

// 获取状态文本
function getStatusText(status) {
    const texts = {
        'online': '在线',
        'offline': '离线',
        'warning': '警告',
        'error': '故障'
    };
    return texts[status] || '未知';
}

// 获取相对时间
function getTimeAgo(date) {
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}小时前`;
    
    const days = Math.floor(hours / 24);
    return `${days}天前`;
}

// 搜索设备
function searchDevices(query) {
    const rows = document.querySelectorAll('.device-row');
    
    rows.forEach(row => {
        const deviceName = row.querySelector('.device-name strong').textContent;
        const deviceLocation = row.querySelector('.device-location').textContent;
        
        if (deviceName.toLowerCase().includes(query.toLowerCase()) ||
            deviceLocation.toLowerCase().includes(query.toLowerCase())) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// 筛选设备
function filterDevices(status) {
    const rows = document.querySelectorAll('.device-row');
    
    rows.forEach(row => {
        const deviceStatus = row.querySelector('.status-badge').className;
        
        if (!status || deviceStatus.includes(`status-${status}`)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// 刷新数据
function refreshData() {
    showNotification('正在刷新数据...', 'info');
    
    // 模拟数据更新
    setTimeout(() => {
        generateDeviceData();
        updateDeviceTable();
        updateOverviewCards();
        updateLastSyncTime();
        showNotification('数据刷新完成', 'success');
    }, 1000);
}

// 更新概览卡片
function updateOverviewCards() {
    const totalDevices = deviceData.length;
    const onlineDevices = deviceData.filter(d => d.status === 'online').length;
    const alertCount = deviceData.filter(d => d.status === 'warning' || d.status === 'error').length;
    const totalPower = deviceData.reduce((sum, d) => sum + d.power, 0).toFixed(1);
    
    // 添加数值更新动画
    updateValueWithAnimation('totalDevices', totalDevices);
    updateValueWithAnimation('onlineDevices', onlineDevices);
    updateValueWithAnimation('alertCount', alertCount);
    updateValueWithAnimation('totalPower', totalPower + 'MW');
}

// 带动画的数值更新
function updateValueWithAnimation(elementId, newValue) {
    const element = document.getElementById(elementId);
    if (element) {
        element.classList.add('number-update');
        element.textContent = newValue;
        
        setTimeout(() => {
            element.classList.remove('number-update');
        }, 500);
    }
}

// 更新最后同步时间
function updateLastSyncTime() {
    const element = document.getElementById('lastSync');
    if (element) {
        element.textContent = new Date().toLocaleString('zh-CN');
    }
}

// 开始数据模拟
function startDataSimulation() {
    // 每30秒更新一次数据
    setInterval(() => {
        if (isLoggedIn) {
            simulateDataChanges();
            updateDeviceTable();
            updateOverviewCards();
            updateLastSyncTime();
        }
    }, 30000);
}

// 模拟数据变化
function simulateDataChanges() {
    deviceData.forEach(device => {
        // 随机更新温度
        if (Math.random() < 0.3) {
            device.temperature += (Math.random() - 0.5) * 2;
            device.temperature = Math.max(16, Math.min(30, device.temperature));
            device.temperature = Math.round(device.temperature * 10) / 10;
        }
        
        // 随机更新湿度
        if (Math.random() < 0.2) {
            device.humidity += Math.floor((Math.random() - 0.5) * 10);
            device.humidity = Math.max(30, Math.min(80, device.humidity));
        }
        
        // 更新最后更新时间
        device.lastUpdate = new Date();
    });
}

// 显示通知
function showNotification(message, type = 'info') {
    const container = document.getElementById('notificationContainer');
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    
    const icon = {
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️'
    }[type] || 'ℹ️';
    
    notification.innerHTML = `
        <span class="notification-icon">${icon}</span>
        <span class="notification-message">${message}</span>
        <button class="notification-close" onclick="closeNotification(this)">×</button>
    `;
    
    container.appendChild(notification);
    
    // 自动关闭
    setTimeout(() => {
        if (notification.parentNode) {
            closeNotification(notification.querySelector('.notification-close'));
        }
    }, type === 'error' ? 5000 : 3000);
}

// 关闭通知
function closeNotification(button) {
    const notification = button.parentNode;
    notification.style.animation = 'slideOut 0.3s ease-out';
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

// 处理窗口大小变化
function handleResize() {
    // 响应式处理逻辑
    const sidebar = document.getElementById('sidebar');
    if (window.innerWidth < 1200) {
        sidebar.classList.add('collapsed');
    } else {
        sidebar.classList.remove('collapsed');
    }
}

// 初始化图表
function initializeCharts() {
    // 这里可以集成Chart.js或其他图表库
    console.log('Charts initialized');
}

// 设备操作函数
function viewDevice(deviceId) {
    showModal('设备详情', `查看设备 ${deviceId} 的详细信息`);
}

function debugDevice(deviceId) {
    showModal('设备调试', `进入设备 ${deviceId} 的调试模式`);
}

// 显示模态框
function showModal(title, content) {
    const modal = document.getElementById('modalOverlay');
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');
    
    modalTitle.textContent = title;
    modalBody.innerHTML = content;
    modal.style.display = 'flex';
}

// 关闭模态框
function closeModal() {
    const modal = document.getElementById('modalOverlay');
    modal.style.display = 'none';
}

// 添加CSS动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .notification {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        margin-bottom: 8px;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease-out;
    }
    
    .notification-success { background: #F3F9F1; border-left: 4px solid #107C10; }
    .notification-error { background: #FDF3F4; border-left: 4px solid #D13438; }
    .notification-warning { background: #FFF8F0; border-left: 4px solid #FF8C00; }
    .notification-info { background: #F3F9FF; border-left: 4px solid #0078D4; }
    
    .notification-container {
        position: fixed;
        top: 80px;
        right: 20px;
        z-index: 10000;
        max-width: 400px;
    }
    
    .notification-close {
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
        margin-left: auto;
    }
    
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 10000;
    }
    
    .modal {
        background: white;
        border-radius: 8px;
        min-width: 400px;
        max-width: 80%;
        max-height: 80%;
        overflow: auto;
    }
    
    .modal-header {
        padding: 16px 20px;
        border-bottom: 1px solid #EDEBE9;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .modal-footer {
        padding: 16px 20px;
        border-top: 1px solid #EDEBE9;
        display: flex;
        gap: 8px;
        justify-content: flex-end;
    }
    
    .modal-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
    }
`;
document.head.appendChild(style);
