<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商用空调监控软件 - UI/UX设计项目展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Microsoft YaHei UI', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #323130;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #323130;
        }

        .card p {
            color: #605E5C;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .card-features {
            list-style: none;
            margin-bottom: 25px;
        }

        .card-features li {
            padding: 5px 0;
            color: #605E5C;
            position: relative;
            padding-left: 20px;
        }

        .card-features li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #107C10;
            font-weight: bold;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #0078D4;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        .btn:hover {
            background: #106EBE;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: transparent;
            color: #0078D4;
            border: 2px solid #0078D4;
        }

        .btn-secondary:hover {
            background: #0078D4;
            color: white;
        }

        .prototype-section {
            background: white;
            border-radius: 12px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .prototype-preview {
            text-align: center;
            margin: 30px 0;
        }

        .prototype-preview img {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }

        .tech-specs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .spec-item {
            background: #FAF9F8;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #0078D4;
        }

        .spec-item h4 {
            color: #323130;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .spec-item p {
            color: #605E5C;
            font-size: 14px;
        }

        .footer {
            text-align: center;
            color: white;
            opacity: 0.8;
            margin-top: 60px;
        }

        .login-info {
            background: #FFF8F0;
            border: 2px solid #FF8C00;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .login-info h4 {
            color: #FF8C00;
            margin-bottom: 10px;
        }

        .login-info p {
            color: #323130;
            margin: 5px 0;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .cards-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 商用空调监控软件</h1>
            <p>专业的UI/UX设计解决方案 - 基于Microsoft Fluent Design System</p>
        </div>

        <div class="cards-grid">
            <!-- 设计规范文档 -->
            <div class="card">
                <span class="card-icon">📋</span>
                <h3>UI设计规范文档</h3>
                <p>完整的设计系统规范，包含色彩、字体、间距、组件库等核心设计元素。</p>
                <ul class="card-features">
                    <li>Microsoft Fluent Design System</li>
                    <li>完整的色彩和字体系统</li>
                    <li>响应式设计适配规则</li>
                    <li>国际化设计规范</li>
                    <li>无障碍设计要求</li>
                </ul>
                <a href="docs/UI设计规范文档.md" class="btn" target="_blank">查看文档</a>
            </div>

            <!-- 组件库规范 -->
            <div class="card">
                <span class="card-icon">🧩</span>
                <h3>组件库设计规范</h3>
                <p>专为HVAC行业定制的组件库，包含基础组件和专业组件的详细规范。</p>
                <ul class="card-features">
                    <li>基础UI组件（按钮、输入框等）</li>
                    <li>专业组件（设备卡片、参数面板）</li>
                    <li>图表组件（实时图表、仪表盘）</li>
                    <li>完整的图标系统</li>
                    <li>组件使用指南</li>
                </ul>
                <a href="docs/组件库设计规范.md" class="btn" target="_blank">查看规范</a>
            </div>

            <!-- 交互原型 -->
            <div class="card">
                <span class="card-icon">🎮</span>
                <h3>交互原型设计</h3>
                <p>可交互的高保真原型，展示完整的用户操作流程和界面效果。</p>
                <ul class="card-features">
                    <li>完整的登录和主界面流程</li>
                    <li>实时数据更新模拟</li>
                    <li>多语言切换演示</li>
                    <li>响应式布局适配</li>
                    <li>丰富的交互动画效果</li>
                </ul>
                <a href="prototype/index.html" class="btn" target="_blank">体验原型</a>
                <a href="docs/交互原型设计方案.md" class="btn btn-secondary" target="_blank">设计方案</a>
            </div>
        </div>

        <!-- 原型演示区域 -->
        <div class="prototype-section">
            <h2>🖥️ 交互原型演示</h2>
            <p>点击下方按钮体验完整的交互原型，包含登录系统、主界面导航、设备监控等核心功能。</p>
            
            <div class="login-info">
                <h4>🔑 原型登录信息</h4>
                <p><strong>用户名:</strong> admin</p>
                <p><strong>密码:</strong> admin</p>
                <p><strong>功能:</strong> 支持多语言切换、主题切换、实时数据模拟</p>
            </div>

            <div class="prototype-preview">
                <a href="prototype/index.html" class="btn" target="_blank" style="font-size: 18px; padding: 16px 32px;">
                    🚀 启动交互原型
                </a>
            </div>

            <div class="tech-specs">
                <div class="spec-item">
                    <h4>🎨 设计风格</h4>
                    <p>Microsoft Fluent Design System，现代化、专业化的界面设计</p>
                </div>
                <div class="spec-item">
                    <h4>🌐 多语言支持</h4>
                    <p>支持中文、英文、德文等多语言切换，完整的国际化方案</p>
                </div>
                <div class="spec-item">
                    <h4>📱 响应式设计</h4>
                    <p>适配不同屏幕尺寸，支持1024px以上分辨率的桌面应用</p>
                </div>
                <div class="spec-item">
                    <h4>⚡ 实时数据</h4>
                    <p>模拟真实的设备数据更新，包含温度、湿度、状态等参数</p>
                </div>
            </div>
        </div>

        <!-- 技术实现建议 -->
        <div class="prototype-section">
            <h2>🔧 技术实现建议</h2>
            <p>基于.NET/WPF技术栈的实现建议和最佳实践。</p>
            
            <div class="tech-specs">
                <div class="spec-item">
                    <h4>🏗️ 架构模式</h4>
                    <p>MVVM架构模式，清晰的分层设计，便于维护和扩展</p>
                </div>
                <div class="spec-item">
                    <h4>🎯 技术栈</h4>
                    <p>.NET 8.0 + WPF，现代化的桌面应用开发技术</p>
                </div>
                <div class="spec-item">
                    <h4>📊 数据绑定</h4>
                    <p>双向数据绑定，实时UI更新，优秀的用户体验</p>
                </div>
                <div class="spec-item">
                    <h4>🔒 安全性</h4>
                    <p>用户权限管理，数据加密传输，符合企业安全要求</p>
                </div>
            </div>
        </div>

        <!-- 项目文件结构 -->
        <div class="prototype-section">
            <h2>📁 项目交付物</h2>
            <p>完整的设计文档、原型文件和开发资源。</p>
            
            <div style="background: #F3F2F1; padding: 20px; border-radius: 8px; font-family: 'Consolas', monospace; font-size: 14px; margin: 20px 0;">
                <pre>商用空调监控软件设计/
├── docs/                           # 设计文档
│   ├── UI设计规范文档.md            # 核心设计规范
│   ├── 组件库设计规范.md            # 组件库详细说明
│   └── 交互原型设计方案.md          # 交互设计方案
├── prototype/                      # 交互原型
│   ├── index.html                  # 主原型文件
│   ├── styles.css                  # 样式表
│   └── script.js                   # 交互脚本
├── assets/                         # 设计资源 (待创建)
│   ├── icons/                      # 图标库
│   ├── images/                     # 图片资源
│   └── fonts/                      # 字体文件
├── README.md                       # 项目说明文档
├── start-prototype.bat             # 原型启动脚本
└── index.html                      # 项目展示页面</pre>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="README.md" class="btn" target="_blank">📖 查看完整项目说明</a>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 商用空调监控软件UI/UX设计项目 | 基于Microsoft Fluent Design System</p>
            <p>专业 · 高效 · 易用的工业软件界面解决方案</p>
        </div>
    </div>
</body>
</html>
