# 商用空调监控软件 - 组件库设计规范

## 📋 组件库概述

本组件库基于 **Microsoft Fluent Design System** 设计，专为商用空调监控调试软件定制，确保界面的一致性、可用性和专业性。

---

## 🎯 基础组件 (Basic Components)

### 1. 按钮组件 (Button)

#### 主要按钮 (Primary Button)
```
用途：主要操作，如"保存"、"应用设置"、"启动设备"
样式规范：
- 背景色：#0078D4
- 文字色：#FFFFFF
- 字体：14px, SemiBold
- 内边距：8px 16px
- 圆角：2px
- 最小宽度：80px
- 高度：32px

状态变化：
- 悬停：背景色 #106EBE，向上移动1px
- 按下：背景色 #005A9E
- 禁用：背景色 #F3F2F1，文字色 #A19F9D
- 加载：显示旋转图标，文字变为"处理中..."
```

#### 次要按钮 (Secondary Button)
```
用途：次要操作，如"取消"、"重置"、"查看详情"
样式规范：
- 背景色：透明
- 边框：1px solid #0078D4
- 文字色：#0078D4
- 其他尺寸同主要按钮

状态变化：
- 悬停：背景色 #F3F2F1
- 按下：背景色 #EDEBE9
- 禁用：边框色 #A19F9D，文字色 #A19F9D
```

#### 危险按钮 (Danger Button)
```
用途：危险操作，如"删除"、"强制停止"、"重置系统"
样式规范：
- 背景色：#D13438
- 文字色：#FFFFFF
- 其他尺寸同主要按钮

状态变化：
- 悬停：背景色 #A4262C
- 按下：背景色 #8B1E22
- 需要二次确认对话框
```

#### 图标按钮 (Icon Button)
```
用途：工具栏操作，如刷新、设置、帮助
样式规范：
- 尺寸：32x32px
- 图标：16x16px
- 背景：透明
- 圆角：2px

状态变化：
- 悬停：背景色 #F3F2F1
- 按下：背景色 #EDEBE9
- 激活：背景色 #DEECF9，边框 #0078D4
```

### 2. 输入组件 (Input Controls)

#### 文本输入框 (Text Input)
```
样式规范：
- 边框：1px solid #EDEBE9
- 背景：#FFFFFF
- 内边距：8px 12px
- 高度：32px
- 字体：14px, Regular
- 圆角：2px

状态变化：
- 聚焦：边框 2px solid #0078D4
- 错误：边框 2px solid #D13438
- 禁用：背景 #F3F2F1，文字色 #A19F9D
- 只读：背景 #FAF9F8

占位符文本：
- 颜色：#A19F9D
- 示例："请输入设备名称..."
```

#### 数值输入框 (Number Input)
```
专用于温度、湿度、压力等数值输入
样式规范：
- 基础样式同文本输入框
- 右侧增减按钮：24x16px
- 单位标签：右侧显示，如"°C"、"%"、"kPa"

验证规则：
- 实时验证数值范围
- 超出范围显示警告色边框
- 提供合理的默认值
```

#### 下拉选择框 (Select)
```
样式规范：
- 基础样式同文本输入框
- 下拉箭头：右侧，#605E5C
- 下拉面板：白色背景，阴影

选项样式：
- 高度：32px
- 内边距：8px 12px
- 悬停：背景 #F3F2F1
- 选中：背景 #DEECF9，文字色 #0078D4

分组支持：
- 分组标题：12px, SemiBold, #605E5C
- 分组间距：4px
```

#### 开关组件 (Toggle Switch)
```
用途：启用/禁用功能，如"自动模式"、"定时开关"
样式规范：
- 宽度：44px
- 高度：20px
- 滑块：16x16px
- 关闭状态：背景 #EDEBE9，滑块 #FFFFFF
- 开启状态：背景 #0078D4，滑块 #FFFFFF

动画效果：
- 切换动画：200ms ease-out
- 滑块移动：smooth transition
```

### 3. 数据展示组件

#### 数据卡片 (Data Card)
```
用途：显示设备状态、统计数据
样式规范：
- 背景：#FFFFFF
- 边框：1px solid #EDEBE9
- 圆角：4px
- 内边距：16px
- 阴影：0 2px 4px rgba(0,0,0,0.1)
- 最小高度：120px

内容结构：
┌─────────────────────────┐
│ 图标 + 标题             │
│ 主要数值 (大字体)       │
│ 副标题/单位             │
│ 状态指示器              │
└─────────────────────────┘

状态变化：
- 悬停：阴影增强，向上移动2px
- 点击：可展开详细信息
```

#### 状态指示器 (Status Indicator)
```
圆点指示器：
- 在线：#107C10 (绿色)
- 离线：#A19F9D (灰色)
- 警告：#FF8C00 (橙色)
- 错误：#D13438 (红色)
- 尺寸：8px 直径

文字指示器：
- 在线：绿色背景，白色文字
- 离线：灰色背景，白色文字
- 警告：橙色背景，白色文字
- 错误：红色背景，白色文字
- 内边距：4px 8px
- 圆角：12px
```

#### 进度条 (Progress Bar)
```
用途：显示加载进度、完成度
样式规范：
- 高度：4px
- 背景：#EDEBE9
- 进度色：#0078D4
- 圆角：2px

变体：
- 确定进度：显示百分比
- 不确定进度：动画效果
- 环形进度：用于仪表板
```

---

## 📊 专业组件 (Professional Components)

### 1. 设备监控组件

#### 设备卡片 (Device Card)
```
专用于显示单个空调设备状态
尺寸：200x150px

布局结构：
┌─────────────────────────┐
│ 🏢 AC-001    [状态指示] │
│                         │
│     22°C                │
│   目标温度               │
│                         │
│ 湿度: 45%  功率: 1.2kW  │
│ 最后更新: 2分钟前       │
└─────────────────────────┘

交互功能：
- 点击：显示详细信息
- 右键：快捷操作菜单
- 双击：进入调试模式
```

#### 参数监控面板 (Parameter Panel)
```
用途：实时显示多个参数
布局：网格式排列

单个参数显示：
┌─────────────┐
│ 温度        │
│ 22.5°C      │
│ ↑ +0.3      │
│ [趋势图标]  │
└─────────────┘

颜色编码：
- 正常：#107C10
- 警告：#FF8C00  
- 异常：#D13438
- 趋势箭头：↑↓→
```

### 2. 图表组件

#### 实时折线图 (Real-time Line Chart)
```
用途：显示温度、湿度等参数的时间趋势
特性：
- 实时数据更新
- 多条线支持
- 缩放和平移
- 数据点提示
- 时间范围选择

样式规范：
- 网格线：#EDEBE9
- 坐标轴：#605E5C
- 数据线：根据参数类型着色
- 背景：#FFFFFF
```

#### 仪表盘图表 (Gauge Chart)
```
用途：显示当前值相对于范围的位置
应用：温度计、压力表、功率表

样式规范：
- 外圈：#EDEBE9
- 进度弧：渐变色
- 指针：#323130
- 中心数值：大字体显示
- 范围标记：小字体
```

### 3. 表格组件

#### 设备列表表格 (Device Table)
```
用途：显示多个设备的状态信息
特性：
- 排序功能
- 筛选功能
- 分页显示
- 行选择
- 快捷操作

列定义：
- 设备名称：左对齐，可点击
- 状态：居中，图标+文字
- 温度：右对齐，数值+单位
- 湿度：右对齐，数值+单位
- 最后更新：右对齐，相对时间

样式规范：
- 表头：#FAF9F8 背景
- 奇数行：#FFFFFF
- 偶数行：#FAF9F8
- 悬停行：#F3F2F1
- 选中行：#DEECF9
```

---

## 🎨 图标系统

### 1. 系统图标
```
导航图标：
- 仪表板：📊
- 监控：👁️
- 调试：🔧
- 设置：⚙️
- 用户：👤

操作图标：
- 刷新：🔄
- 搜索：🔍
- 筛选：🔽
- 导出：📤
- 帮助：❓
```

### 2. 状态图标
```
设备状态：
- 在线：🟢
- 离线：⚫
- 警告：🟡
- 错误：🔴
- 维护：🔵

操作状态：
- 成功：✅
- 失败：❌
- 处理中：⏳
- 暂停：⏸️
- 停止：⏹️
```

### 3. 功能图标
```
空调相关：
- 制冷：❄️
- 制热：🔥
- 通风：💨
- 自动：🔄
- 定时：⏰

参数类型：
- 温度：🌡️
- 湿度：💧
- 压力：📊
- 功率：⚡
- 风速：💨
```

---

## 🔧 组件使用指南

### 1. 组件选择原则
```
按钮选择：
- 主要操作 → Primary Button
- 次要操作 → Secondary Button  
- 危险操作 → Danger Button
- 工具操作 → Icon Button

输入选择：
- 文本信息 → Text Input
- 数值参数 → Number Input
- 选项选择 → Select
- 开关功能 → Toggle Switch
```

### 2. 布局组合建议
```
卡片内容：
- 标题 + 主要数据 + 状态
- 避免信息过载
- 保持视觉层次

表单设计：
- 标签在上，输入在下
- 相关字段分组
- 必填字段标记*
- 提供输入提示
```

### 3. 响应式适配
```
组件缩放：
- 按钮：保持最小尺寸
- 输入框：宽度自适应
- 卡片：网格布局调整
- 表格：隐藏次要列
```

---

*组件库将持续完善，确保满足空调监控软件的专业需求。*
